{"createdAt": "2025-06-24T19:26:00.228Z", "updatedAt": "2025-06-24T20:04:15.999Z", "id": "R0ZHXOV9mCNSLrM8", "name": "gtree_build_context", "active": false, "isArchived": false, "nodes": [{"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-120, -40], "id": "b31b8f00-7044-459c-afcb-50aed612ce68", "name": "When Executed by Another Workflow"}, {"parameters": {"workflowId": {"__rl": true, "value": "1iddyW0Iw3tks4Sf", "mode": "list", "cachedResultName": "gtree_get"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"repo": "={{ $json.repo }}", "owner": "={{ $json.owner }}"}, "matchingColumns": [], "schema": [{"id": "repo", "displayName": "repo", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "owner", "displayName": "owner", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [320, -40], "id": "ec7d6966-c0ed-426d-a939-74617a62bee3", "name": "Execute Workflow"}, {"parameters": {"mode": "raw", "jsonOutput": "{\n  \"repo\": \"ai-svg-generator\",\n  \"owner\": \"RustedVikingOG\"\n}\n", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [100, -40], "id": "0f717b8f-daba-40af-8e6e-291482d798e4", "name": "<PERSON>"}, {"parameters": {"resource": "file", "operation": "get", "owner": {"__rl": true, "value": "={{ $('Edit Fields').item.json.owner }}", "mode": "name"}, "repository": {"__rl": true, "value": "={{ $('Edit Fields').item.json.repo }}", "mode": "name"}, "filePath": "={{ $json.appended_path }}", "asBinaryProperty": false, "additionalParameters": {}}, "type": "n8n-nodes-base.github", "typeVersion": 1.1, "position": [1200, -40], "id": "bbdbe299-7e6d-4455-a9c4-29f4f43576fd", "name": "GitHub", "webhookId": "1842ba48-9ae4-4d7d-8612-7ede87ed7398", "credentials": {"githubApi": {"id": "uJglpW9ievZkAsDT", "name": "GitHub account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "0df93a53-2cf3-4e89-9cf9-1afed7b7a2bd", "leftValue": "={{ $json.path }}", "rightValue": ".png", "operator": {"type": "string", "operation": "notContains"}}, {"id": "6fd72d3c-0ef4-44ae-98e9-9e5908ad7b02", "leftValue": "={{ $json.path }}", "rightValue": ".svg", "operator": {"type": "string", "operation": "notContains"}}, {"id": "94aba0dc-f306-4cc5-80bb-0212006a1d94", "leftValue": "={{ $json.path }}", "rightValue": ".ico", "operator": {"type": "string", "operation": "notContains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [560, -40], "id": "9c6d7409-5e51-484a-8fb3-407ee88a9008", "name": "Filter"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1420, -40], "id": "0d4fdd51-30b4-40be-86f9-e1e59f913102", "name": "Loop Over Items"}, {"parameters": {"url": "https://raw.githubusercontent.com/RustedVikingOG/ai-svg-generator/main/frontend/src/components/ChatInterface.vue", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [780, -40], "id": "2a711b3b-8ae2-4b24-8cd4-6a6ba2c49636", "name": "HTTP Request"}], "connections": {"When Executed by Another Workflow": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "GitHub": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "GitHub", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "0fdcffc3-2920-4e73-a74f-616a667da8ec", "triggerCount": 0, "tags": [{"createdAt": "2025-06-24T19:24:09.552Z", "updatedAt": "2025-06-24T19:24:09.552Z", "id": "UrrRlGpBiXZQ14iY", "name": "docugen"}]}